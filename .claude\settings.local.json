{"permissions": {"allow": ["<PERSON><PERSON>(msbuild:*)", "Bash(dotnet build:*)", "<PERSON><PERSON>(csc:*)", "Bash(ls:*)", "Bash(find:*)", "Bash(\"/mnt/c/Windows/Microsoft.NET/Framework64/v4.0.30319/csc.exe\" /target:library /reference:\"Sansar-Scripting-Knowledge-Base/Assemblies/Sansar.Script.dll\" /reference:\"Sansar-Scripting-Knowledge-Base/Assemblies/Sansar.Simulation.dll\" /out:\"bin/HelloSansarTest.dll\" HelloSansarTest.cs)", "Bash(\"/mnt/c/Windows/Microsoft.NET/Framework64/v4.0.30319/csc.exe\" /target:library /reference:\"Sansar-Scripting-Knowledge-Base/Assemblies/Sansar.Script.dll\" /reference:\"Sansar-Scripting-Knowledge-Base/Assemblies/Sansar.Simulation.dll\" /out:\"bin/GameManager.dll\" GameManager.cs)", "Bash(\"/mnt/c/Windows/Microsoft.NET/Framework64/v4.0.30319/csc.exe\" /target:library /reference:\"Sansar-Scripting-Knowledge-Base/Assemblies/Sansar.Script.dll\" /reference:\"Sansar-Scripting-Knowledge-Base/Assemblies/Sansar.Simulation.dll\" /out:\"bin/GameManagerSimple.dll\" GameManagerSimple.cs)", "Bash(grep:*)", "Bash(\"/mnt/c/Windows/Microsoft.NET/Framework64/v4.0.30319/csc.exe\" /target:library /reference:\"Sansar-Scripting-Knowledge-Base/Assemblies/Sansar.Script.dll\" /reference:\"Sansar-Scripting-Knowledge-Base/Assemblies/Sansar.Simulation.dll\" /out:\"bin/GridManager.dll\" GridManager.cs)", "Bash(\"/mnt/c/Windows/Microsoft.NET/Framework64/v4.0.30319/csc.exe\" /target:library /reference:\"Sansar-Scripting-Knowledge-Base/Assemblies/Sansar.Script.dll\" /reference:\"Sansar-Scripting-Knowledge-Base/Assemblies/Sansar.Simulation.dll\" /out:\"bin/PieceLogic.dll\" PieceLogic.cs)", "Bash(\"/mnt/c/Windows/Microsoft.NET/Framework64/v4.0.30319/csc.exe\" /target:library /reference:\"Sansar-Scripting-Knowledge-Base/Assemblies/Sansar.Script.dll\" /reference:\"Sansar-Scripting-Knowledge-Base/Assemblies/Sansar.Simulation.dll\" /out:\"bin/SeatController.dll\" SeatController.cs)", "Bash(\"/mnt/c/Windows/Microsoft.NET/Framework64/v4.0.30319/csc.exe\" /target:library /reference:\"Sansar-Scripting-Knowledge-Base/Assemblies/Sansar.Script.dll\" /reference:\"Sansar-Scripting-Knowledge-Base/Assemblies/Sansar.Simulation.dll\" /out:\"bin/BlockController.dll\" BlockController.cs)", "Bash(\"/mnt/c/Windows/Microsoft.NET/Framework64/v4.0.30319/csc.exe\" /target:library /reference:\"Sansar-Scripting-Knowledge-Base/Assemblies/Sansar.Script.dll\" /reference:\"Sansar-Scripting-Knowledge-Base/Assemblies/Sansar.Simulation.dll\" /out:\"bin/TetrisGame.dll\" TetrisGame.cs)", "Bash(\"/mnt/c/Windows/Microsoft.NET/Framework64/v4.0.30319/csc.exe\" /target:library /reference:\"Sansar-Scripting-Knowledge-Base/Assemblies/Sansar.Script.dll\" /reference:\"Sansar-Scripting-Knowledge-Base/Assemblies/Sansar.Simulation.dll\" /out:\"bin/TetrisGame.dll\" TetrisGame.cs AudioResources.cs)", "Bash(\"/mnt/c/Windows/Microsoft.NET/Framework64/v4.0.30319/csc.exe\" /target:library /reference:\"Sansar-Scripting-Knowledge-Base/Assemblies/Sansar.Script.dll\" /reference:\"Sansar-Scripting-Knowledge-Base/Assemblies/Sansar.Simulation.dll\" /out:\"bin/AudioResources.dll\" AudioResources.cs)", "Bash(cp:*)", "<PERSON><PERSON>(sed:*)", "<PERSON><PERSON>(python3:*)"], "deny": []}}